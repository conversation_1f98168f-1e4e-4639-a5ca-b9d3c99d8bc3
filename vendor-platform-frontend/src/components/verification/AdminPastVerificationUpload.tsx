'use client';

import { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  HStack,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Alert,
  AlertIcon,
  Text,
  useToast,
  Textarea,
  Divider,
} from '@chakra-ui/react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { uploadPastVerification, getVerificationCenters } from '@/actions/verification';
import { FileUploader } from '@/components/verification/FileUploader';
import { fileToBase64 } from '@/utils/fileToBase64';

const validationSchema = Yup.object({
  vehiclePlate: Yup.string()
    .required('Placa del vehículo es requerida')
    .matches(/^[A-Z0-9]{6,8}$/, 'Formato de placa inválido'),
  verificationCenterId: Yup.string().required('Centro de verificación es requerido'),
  verificationDate: Yup.date().required('Fecha de verificación es requerida'),
  hologramType: Yup.string().oneOf(['00', '0', '1', '2'], 'Tipo de holograma inválido'),
});

export function AdminPastVerificationUpload() {
  const [verificationCertificate, setVerificationCertificate] = useState<File | null>(null);
  const [hologramPhoto, setHologramPhoto] = useState<File | null>(null);
  const toast = useToast();

  const { data: verificationCenters, isLoading: centersLoading } = useQuery({
    queryKey: ['verification-centers'],
    queryFn: () => getVerificationCenters(),
  });

  const uploadMutation = useMutation({
    mutationFn: uploadPastVerification,
    onSuccess: () => {
      toast({
        title: 'Verificación registrada',
        description: 'La verificación pasada se ha registrado exitosamente',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      formik.resetForm();
      setVerificationCertificate(null);
      setHologramPhoto(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Error al registrar la verificación',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });

  const formik = useFormik({
    initialValues: {
      vehiclePlate: '',
      verificationCenterId: '',
      verificationDate: '',
      hologramType: '' as '00' | '0' | '1' | '2' | '',
      notes: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        const verificationCertificateBase64 = verificationCertificate
          ? await fileToBase64(verificationCertificate)
          : undefined;
        const hologramPhotoBase64 = hologramPhoto
          ? await fileToBase64(hologramPhoto)
          : undefined;

        uploadMutation.mutate({
          vehiclePlate: values.vehiclePlate.toUpperCase(),
          verificationCenterId: values.verificationCenterId,
          verificationDate: values.verificationDate,
          hologramType: values.hologramType,
          verificationCertificate: verificationCertificateBase64,
          hologramPhoto: hologramPhotoBase64,
          notes: values.notes,
        });
      } catch (error) {
        console.error('Error converting files to base64:', error);
        toast({
          title: 'Error',
          description: 'Error al procesar los archivos',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    },
  });

  const getHologramDescription = (type: string) => {
    switch (type) {
      case '00':
        return 'Exento por 2 años';
      case '0':
        return 'Cumple con la norma (cada 6 meses)';
      case '1':
        return 'Primera verificación reprobada (cada 6 meses)';
      case '2':
        return 'Segunda verificación reprobada (cada 6 meses)';
      default:
        return '';
    }
  };

  return (
    <Card>
      <CardHeader>
        <Heading size="md">Subir Verificación Pasada</Heading>
        <Text fontSize="sm" color="gray.600">
          Registra manualmente verificaciones históricas para vehículos ya activos en el sistema
        </Text>
      </CardHeader>
      <CardBody>
        <form onSubmit={formik.handleSubmit}>
          <VStack spacing={6}>
            <Alert status="info" fontSize="sm">
              <AlertIcon />
              <Box>
                <Text fontWeight="bold">Importante:</Text>
                <Text>
                  Esta función es para registrar verificaciones que ya ocurrieron en el pasado.
                  Solo completa los campos con información que tengas disponible.
                </Text>
              </Box>
            </Alert>

            <HStack spacing={4} width="full">
              <FormControl isRequired isInvalid={!!formik.errors.vehiclePlate && formik.touched.vehiclePlate}>
                <FormLabel>Placa del Vehículo</FormLabel>
                <Input
                  name="vehiclePlate"
                  value={formik.values.vehiclePlate}
                  onChange={(e) => formik.setFieldValue('vehiclePlate', e.target.value.toUpperCase())}
                  onBlur={formik.handleBlur}
                  placeholder="ABC1234"
                  maxLength={8}
                />
                {formik.errors.vehiclePlate && formik.touched.vehiclePlate && (
                  <Text color="red.500" fontSize="sm">{formik.errors.vehiclePlate}</Text>
                )}
              </FormControl>

              <FormControl isRequired isInvalid={!!formik.errors.verificationDate && formik.touched.verificationDate}>
                <FormLabel>Fecha de Verificación</FormLabel>
                <Input
                  type="date"
                  name="verificationDate"
                  value={formik.values.verificationDate}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {formik.errors.verificationDate && formik.touched.verificationDate && (
                  <Text color="red.500" fontSize="sm">{formik.errors.verificationDate}</Text>
                )}
              </FormControl>
            </HStack>

            <FormControl isRequired isInvalid={!!formik.errors.verificationCenterId && formik.touched.verificationCenterId}>
              <FormLabel>Centro de Verificación</FormLabel>
              <Select
                name="verificationCenterId"
                value={formik.values.verificationCenterId}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder="Selecciona un centro de verificación"
                isDisabled={centersLoading}
              >
                {verificationCenters?.map((center: any) => (
                  <option key={center._id} value={center._id}>
                    {center.code} - {center.name} ({center.location})
                  </option>
                ))}
              </Select>
              {formik.errors.verificationCenterId && formik.touched.verificationCenterId && (
                <Text color="red.500" fontSize="sm">{formik.errors.verificationCenterId}</Text>
              )}
            </FormControl>

            <Divider />

            <Text fontWeight="bold" alignSelf="flex-start">Información del Holograma (Opcional)</Text>

            <FormControl>
              <FormLabel>Tipo de Holograma</FormLabel>
              <Select
                name="hologramType"
                value={formik.values.hologramType}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder="Selecciona el tipo de holograma"
              >
                <option value="00">00 - Exento por 2 años</option>
                <option value="0">0 - Cumple con la norma (cada 6 meses)</option>
                <option value="1">1 - Primera verificación reprobada (cada 6 meses)</option>
                <option value="2">2 - Segunda verificación reprobada (cada 6 meses)</option>
              </Select>
              {formik.values.hologramType && (
                <Text fontSize="sm" color="gray.600" mt={1}>
                  {getHologramDescription(formik.values.hologramType)}
                </Text>
              )}
            </FormControl>

            <FormControl>
              <FormLabel>Certificado de Verificación (Opcional)</FormLabel>
              <Text fontSize="sm" color="gray.600" mb={2}>
                Sube una foto o PDF del certificado de verificación si está disponible
              </Text>
              <FileUploader
                accept="image/*,application/pdf"
                onFileSelect={setVerificationCertificate}
                selectedFile={verificationCertificate}
              />
            </FormControl>

            <FormControl>
              <FormLabel>Foto del Holograma (Opcional)</FormLabel>
              <Text fontSize="sm" color="gray.600" mb={2}>
                Sube una foto del holograma de verificación si está disponible
              </Text>
              <FileUploader
                accept="image/*"
                onFileSelect={setHologramPhoto}
                selectedFile={hologramPhoto}
              />
            </FormControl>

            <FormControl>
              <FormLabel>Notas Adicionales</FormLabel>
              <Textarea
                name="notes"
                value={formik.values.notes}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder="Información adicional sobre esta verificación..."
                rows={3}
              />
            </FormControl>

            <Button
              type="submit"
              colorScheme="blue"
              size="lg"
              width="full"
              isLoading={uploadMutation.isPending}
              loadingText="Registrando..."
            >
              Registrar Verificación Pasada
            </Button>
          </VStack>
        </form>
      </CardBody>
    </Card>
  );
}
