import { EmissionsVerificationService } from './emissions-verification.service';
import { VerificationLogicService } from './verification-logic.service';
import { EmissionsVerification } from '../models/emissions-verification.model';
import { transporter, emailSender } from '@/middlewares/email';
import { logger } from '@/clean/lib/logger';

export interface NotificationData {
  vehiclePlate: string;
  nextVerificationDate: Date;
  daysRemaining: number;
  verificationCenter?: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
}

export class NotificationService {
  /**
   * Envía recordatorio al customer cuando se acerca la verificación
   */
  static async sendCustomerReminder(verification: any): Promise<boolean> {
    try {
      const daysRemaining = Math.ceil(
        (verification.nextVerificationDate.getTime() - new Date().getTime()) / (1000 * 3600 * 24)
      );

      const notificationData: NotificationData = {
        vehiclePlate: verification.vehiclePlate,
        nextVerificationDate: verification.nextVerificationDate,
        daysRemaining,
        verificationCenter: verification.verificationCenter?.name,
      };

      // Aquí se integraría con el sistema de notificaciones existente
      // Por ejemplo: WhatsApp, Email, SMS, etc.
      console.log(`Sending customer reminder for vehicle ${verification.vehiclePlate}:`, notificationData);

      // Simular envío exitoso
      await EmissionsVerificationService.markReminderSent(verification._id);

      return true;
    } catch (error) {
      console.error('Error sending customer reminder:', error);
      return false;
    }
  }

  /**
   * Envía notificación a la flota para vencimientos del mes siguiente
   */
  static async sendFleetReminder(verifications: any[]): Promise<boolean> {
    try {
      const fleetData = verifications.map((v) => ({
        vehiclePlate: v.vehiclePlate,
        nextVerificationDate: v.nextVerificationDate,
        verificationCenter: v.verificationCenter?.name,
        status: v.status,
      }));

      console.log(`Sending fleet reminder for ${verifications.length} vehicles:`, fleetData);

      // Aquí se integraría con el sistema de notificaciones para fleets
      // Podría ser un reporte mensual, dashboard, etc.

      return true;
    } catch (error) {
      console.error('Error sending fleet reminder:', error);
      return false;
    }
  }

  /**
   * Envía recordatorio diario al customer que no ha subido evidencias
   */
  static async sendDailyCustomerReminder(verification: any): Promise<boolean> {
    try {
      const daysSinceVerification = Math.ceil(
        (new Date().getTime() - verification.verificationDate.getTime()) / (1000 * 3600 * 24)
      );

      logger.info(
        `[sendDailyCustomerReminder] Daily reminder for vehicle ${verification.vehiclePlate} - ${daysSinceVerification} days since verification`
      );

      // Send email reminder to customer
      const emailHtml = this.generateReminderEmailTemplate({
        vehiclePlate: verification.vehiclePlate,
        verificationDate: verification.verificationDate,
        uniqueLink: verification.uniqueLink,
        daysSinceVerification,
      });

      try {
        await transporter.sendMail({
          from: emailSender,
          to: verification.customerEmail || '<EMAIL>', // TODO: Get actual customer email
          subject: `Recordatorio: Completa tu verificación vehicular - Placa ${verification.vehiclePlate}`,
          html: emailHtml,
        });

        logger.info(`[sendDailyCustomerReminder] Email reminder sent successfully for vehicle ${verification.vehiclePlate}`);
      } catch (emailError: any) {
        logger.error(`[sendDailyCustomerReminder] Error sending email reminder for vehicle ${verification.vehiclePlate}`, {
          message: emailError.message,
          stack: emailError.stack,
        });
      }

      // Incrementar contador de recordatorios
      await EmissionsVerification.findByIdAndUpdate(verification._id, {
        $inc: { remindersSent: 1 },
        lastReminderSent: new Date(),
      });

      return true;
    } catch (error: any) {
      logger.error('[sendDailyCustomerReminder] Error sending daily customer reminder:', {
        message: error.message,
        stack: error.stack,
      });
      return false;
    }
  }

  /**
   * Proceso automático para enviar recordatorios
   */
  static async processReminders(): Promise<void> {
    try {
      // 1. Recordatorios para customers que se acerca la verificación (30 días)
      const upcomingVerifications = await EmissionsVerificationService.getVerificationsNeedingReminder(30);

      for (const verification of upcomingVerifications) {
        if (VerificationLogicService.needsVerificationSoon(verification.nextVerificationDate, 30)) {
          await this.sendCustomerReminder(verification);
        }
      }

      // 2. Recordatorios diarios para customers que no han subido evidencias
      const pendingCustomerVerifications = await EmissionsVerification.find({
        status: 'pending_customer',
        createdAt: { $lte: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Más de 1 día
      }).populate('verificationCenter');

      for (const verification of pendingCustomerVerifications) {
        // Enviar recordatorio máximo cada 24 horas
        const lastReminder = verification.lastReminderSent;
        if (!lastReminder || new Date().getTime() - lastReminder.getTime() >= 24 * 60 * 60 * 1000) {
          await this.sendDailyCustomerReminder(verification);
        }
      }

      // 3. Recordatorio mensual para fleets (primer día del mes)
      const today = new Date();
      if (today.getDate() === 1) {
        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
        const endOfNextMonth = new Date(today.getFullYear(), today.getMonth() + 2, 0);

        const nextMonthVerifications = await EmissionsVerification.find({
          status: 'completed',
          nextVerificationDate: {
            $gte: nextMonth,
            $lte: endOfNextMonth,
          },
        }).populate('verificationCenter');

        if (nextMonthVerifications.length > 0) {
          await this.sendFleetReminder(nextMonthVerifications);
        }
      }

      logger.info('[processReminders] Reminder processing completed successfully');
    } catch (error: any) {
      logger.error('[processReminders] Error processing reminders:', {
        message: error.message,
        stack: error.stack,
      });
    }
  }

  /**
   * Configurar cron job para recordatorios automáticos
   */
  static setupAutomaticReminders(): void {
    // Este método se llamaría desde el main app para configurar el cron job
    // Ejemplo usando node-cron:
    /*
    const cron = require('node-cron');

    // Ejecutar todos los días a las 9:00 AM
    cron.schedule('0 9 * * *', () => {
      console.log('Running automatic reminders...');
      this.processReminders();
    });
    */

    console.log('Automatic reminders system configured');
  }

  /**
   * Envía notificación cuando customer completa el proceso
   */
  static async notifyVerificationCompleted(verification: any): Promise<boolean> {
    try {
      logger.info(`[notifyVerificationCompleted] Verification completed for vehicle ${verification.vehiclePlate}`);
      logger.info(`[notifyVerificationCompleted] Next verification due: ${verification.nextVerificationDate}`);

      // Aquí se podría enviar confirmación al customer
      // y notificación al centro de verificación

      return true;
    } catch (error: any) {
      logger.error('[notifyVerificationCompleted] Error sending completion notification:', {
        message: error.message,
        stack: error.stack,
      });
      return false;
    }
  }

  /**
   * Genera el template HTML para el email de recordatorio
   */
  private static generateReminderEmailTemplate(data: {
    vehiclePlate: string;
    verificationDate: Date;
    uniqueLink: string;
    daysSinceVerification: number;
  }): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Recordatorio de Verificación Vehicular</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f8f9fa; }
          .button { display: inline-block; padding: 12px 24px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚗 Recordatorio de Verificación Vehicular</h1>
          </div>

          <div class="content">
            <h2>¡Hola!</h2>

            <p>Te recordamos que aún tienes pendiente completar el proceso de verificación vehicular para tu vehículo con placa <strong>${data.vehiclePlate}</strong>.</p>

            <div class="warning">
              <strong>⏰ Días transcurridos:</strong> ${data.daysSinceVerification} días desde la verificación (${data.verificationDate.toLocaleDateString('es-MX')})
            </div>

            <p>Para completar tu verificación, necesitas subir:</p>
            <ul>
              <li>📄 Certificado de verificación</li>
              <li>📅 Fecha de la próxima verificación (si aparece en el certificado)</li>
              <li>🏷️ Tipo de holograma obtenido (00, 0, 1, 2)</li>
              <li>📸 Foto clara del holograma de verificación</li>
            </ul>

            <div style="text-align: center;">
              <a href="${process.env.FRONTEND_URL}/verification/customer/${data.uniqueLink}" class="button">
                Completar Verificación Ahora
              </a>
            </div>

            <p><strong>¿Por qué es importante completar este proceso?</strong></p>
            <ul>
              <li>✅ Mantener tu vehículo en regla</li>
              <li>📊 Tener un historial completo de verificaciones</li>
              <li>🔔 Recibir recordatorios automáticos para futuras verificaciones</li>
            </ul>

            <p>Si tienes alguna duda o problema, no dudes en contactarnos.</p>
          </div>

          <div class="footer">
            <p>Este es un recordatorio automático de OneCarNow</p>
            <p>Si ya completaste tu verificación, puedes ignorar este mensaje</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
